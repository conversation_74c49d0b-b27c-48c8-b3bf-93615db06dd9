plugins {
  id 'com.android.library'
  id 'expo-module-gradle-plugin'
}

group = 'host.exp.exponent'
version = '18.1.6'

android {
  namespace "expo.modules.location"
  defaultConfig {
    versionCode 29
    versionName "18.1.6"
    consumerProguardFiles("proguard-rules.pro")
  }
}

dependencies {
  api 'com.google.android.gms:play-services-location:21.0.1'
  implementation 'androidx.annotation:annotation:1.7.1'
}
