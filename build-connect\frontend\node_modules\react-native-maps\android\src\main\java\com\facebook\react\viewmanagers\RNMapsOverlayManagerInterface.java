/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.ReadableMap;

public interface RNMapsOverlayManagerInterface<T extends View> {
  void setBearing(T view, float value);
  void setBounds(T view, @Nullable ReadableMap value);
  void setImage(T view, @Nullable ReadableMap value);
  void setOpacity(T view, float value);
  void setTappable(T view, boolean value);
}
