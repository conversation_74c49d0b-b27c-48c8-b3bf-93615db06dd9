/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaDelegate.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.ColorPropConverter;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.uimanager.BaseViewManager;
import com.facebook.react.uimanager.BaseViewManagerDelegate;
import com.facebook.react.uimanager.LayoutShadowNode;

public class RNMapsMarkerManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNMapsMarkerManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
  public RNMapsMarkerManagerDelegate(U viewManager) {
    super(viewManager);
  }
  @Override
  public void setProperty(T view, String propName, @Nullable Object value) {
    switch (propName) {
      case "anchor":
        mViewManager.setAnchor(view, (ReadableMap) value);
        break;
      case "calloutAnchor":
        mViewManager.setCalloutAnchor(view, (ReadableMap) value);
        break;
      case "image":
        mViewManager.setImage(view, (ReadableMap) value);
        break;
      case "calloutOffset":
        mViewManager.setCalloutOffset(view, (ReadableMap) value);
        break;
      case "displayPriority":
        mViewManager.setDisplayPriority(view, (String) value);
        break;
      case "coordinate":
        mViewManager.setCoordinate(view, (ReadableMap) value);
        break;
      case "description":
        mViewManager.setDescription(view, value == null ? null : (String) value);
        break;
      case "draggable":
        mViewManager.setDraggable(view, value == null ? false : (boolean) value);
        break;
      case "title":
        mViewManager.setTitle(view, value == null ? null : (String) value);
        break;
      case "identifier":
        mViewManager.setIdentifier(view, value == null ? null : (String) value);
        break;
      case "isPreselected":
        mViewManager.setIsPreselected(view, value == null ? false : (boolean) value);
        break;
      case "opacity":
        mViewManager.setOpacity(view, value == null ? 1f : ((Double) value).doubleValue());
        break;
      case "pinColor":
        mViewManager.setPinColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "titleVisibility":
        mViewManager.setTitleVisibility(view, (String) value);
        break;
      case "subtitleVisibility":
        mViewManager.setSubtitleVisibility(view, (String) value);
        break;
      case "useLegacyPinView":
        mViewManager.setUseLegacyPinView(view, value == null ? false : (boolean) value);
        break;
      default:
        super.setProperty(view, propName, value);
    }
  }

  @Override
  public void receiveCommand(T view, String commandName, @Nullable ReadableArray args) {
    switch (commandName) {
      case "animateToCoordinates":
        mViewManager.animateToCoordinates(view, args.getDouble(0), args.getDouble(1), args.getInt(2));
        break;
      case "setCoordinates":
        mViewManager.setCoordinates(view, args.getDouble(0), args.getDouble(1));
        break;
      case "showCallout":
        mViewManager.showCallout(view);
        break;
      case "hideCallout":
        mViewManager.hideCallout(view);
        break;
      case "redrawCallout":
        mViewManager.redrawCallout(view);
        break;
      case "redraw":
        mViewManager.redraw(view);
        break;
    }
  }
}
