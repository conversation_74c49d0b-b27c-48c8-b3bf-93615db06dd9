{"version": 3, "file": "LocationSubscribers.js", "sourceRoot": "", "sources": ["../src/LocationSubscribers.ts"], "names": [], "mappings": "AAEA,OAAO,YAAY,MAAM,gBAAgB,CAAC;AAE1C,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAO9D,IAAI,WAAW,GAAG,CAAC,CAAC;AAEpB,MAAM,UAAU;IAGN,SAAS,CAAS;IAClB,cAAc,CAAS;IACvB,SAAS,GAAmC,EAAE,CAAC;IAC/C,iBAAiB,GAA6B,IAAI,CAAC;IAE3D,YAAY,SAAiB,EAAE,cAAsB;QACnD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED,2BAA2B;QACzB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,OAAO;QACT,CAAC;QACD,IAAI,CAAC,iBAAiB,GAAG,oBAAoB,CAAC,WAAW,CACvD,IAAI,CAAC,SAAS,EACd,CAAC,KAAkB,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAC5C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,QAAsB;QACrC,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,MAAM,EAAE,GAAG,EAAE,WAAW,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC;QAC9B,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;;;OAIG;IACH,qBAAqB,CAAC,OAAe,EAAE,QAAsB;QAC3D,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,MAAM,EAAE,GAAG,OAAO,CAAC;QACnB,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC;QAC9B,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,EAAU;QAC3B,2DAA2D;QAC3D,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAC1B,YAAY,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;QAElC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvE,oBAAoB,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAChC,CAAC;IACH,CAAC;IAED,OAAO,CAAC,KAAkB;QACxB,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAEzC,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QACvC,CAAC;aAAM,CAAC;YACN,YAAY,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;CACF;AAED,MAAM,CAAC,MAAM,kBAAkB,GAAG,IAAI,UAAU,CAC9C,sBAAsB,EACtB,UAAU,CACX,CAAC;AACF,MAAM,CAAC,MAAM,iBAAiB,GAAG,IAAI,UAAU,CAC7C,qBAAqB,EACrB,SAAS,CACV,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAAG,IAAI,UAAU,CACnD,oBAAoB,EACpB,QAAQ,CACT,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,kBAAkB;IAChC,OAAO,WAAW,CAAC;AACrB,CAAC", "sourcesContent": ["import { type EventSubscription } from 'expo-modules-core';\n\nimport ExpoLocation from './ExpoLocation';\nimport { LocationCallback, LocationErrorCallback, LocationHeadingCallback } from './Location.types';\nimport { LocationEventEmitter } from './LocationEventEmitter';\n\ntype EventObject = {\n  watchId: number;\n  [key: string]: any;\n};\n\nlet nextWatchId = 0;\n\nclass Subscriber<\n  CallbackType extends LocationCallback | LocationHeadingCallback | LocationErrorCallback,\n> {\n  private eventName: string;\n  private eventDataField: string;\n  private callbacks: { [id: string]: CallbackType } = {};\n  private eventSubscription: EventSubscription | null = null;\n\n  constructor(eventName: string, eventDataField: string) {\n    this.eventName = eventName;\n    this.eventDataField = eventDataField;\n  }\n\n  maybeInitializeSubscription() {\n    if (this.eventSubscription) {\n      return;\n    }\n    this.eventSubscription = LocationEventEmitter.addListener(\n      this.eventName,\n      (event: EventObject) => this.trigger(event)\n    );\n  }\n\n  /**\n   * Registers given callback under new id which is then returned.\n   */\n  registerCallback(callback: CallbackType): number {\n    this.maybeInitializeSubscription();\n    const id = ++nextWatchId;\n    this.callbacks[id] = callback;\n    return id;\n  }\n\n  /**\n   * Registers given callback under and existing id. This can be used to\n   * create a subscriber for the error event on the same id as the location\n   * event is subscribed to.\n   */\n  registerCallbackForId(watchId: number, callback: CallbackType): number {\n    this.maybeInitializeSubscription();\n    const id = watchId;\n    this.callbacks[id] = callback;\n    return id;\n  }\n\n  /**\n   * Unregisters a callback with given id and revokes the subscription if possible.\n   */\n  unregisterCallback(id: number): void {\n    // Do nothing if we have already unregistered the callback.\n    if (!this.callbacks[id]) {\n      return;\n    }\n\n    delete this.callbacks[id];\n    ExpoLocation.removeWatchAsync(id);\n\n    if (Object.keys(this.callbacks).length === 0 && this.eventSubscription) {\n      LocationEventEmitter.removeSubscription(this.eventSubscription);\n      this.eventSubscription = null;\n    }\n  }\n\n  trigger(event: EventObject): void {\n    const watchId = event.watchId;\n    const callback = this.callbacks[watchId];\n\n    if (callback) {\n      callback(event[this.eventDataField]);\n    } else {\n      ExpoLocation.removeWatchAsync(watchId);\n    }\n  }\n}\n\nexport const LocationSubscriber = new Subscriber<LocationCallback>(\n  'Expo.locationChanged',\n  'location'\n);\nexport const HeadingSubscriber = new Subscriber<LocationHeadingCallback>(\n  'Expo.headingChanged',\n  'heading'\n);\n\nexport const LocationErrorSubscriber = new Subscriber<LocationErrorCallback>(\n  'Expo.locationError',\n  'reason'\n);\n\n/**\n * @private Necessary for some unit tests.\n */\nexport function _getCurrentWatchId(): number {\n  return nextWatchId;\n}\n"]}