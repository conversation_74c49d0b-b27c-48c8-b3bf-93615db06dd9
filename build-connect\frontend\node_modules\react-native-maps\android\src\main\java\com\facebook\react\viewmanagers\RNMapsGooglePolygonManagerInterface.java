/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.ReadableArray;

public interface RNMapsGooglePolygonManagerInterface<T extends View> {
  void setCoordinates(T view, @Nullable ReadableArray value);
  void setFillColor(T view, @Nullable Integer value);
  void setStrokeColor(T view, @Nullable Integer value);
  void setGeodesic(T view, boolean value);
  void setHoles(T view, @Nullable ReadableArray value);
  void setTappable(T view, boolean value);
}
