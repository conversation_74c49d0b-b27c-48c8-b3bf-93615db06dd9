{"formatVersion": "1.1", "component": {"group": "host.exp.exponent", "module": "expo.modules.location", "version": "18.1.6", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.13"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.google.android.gms", "module": "play-services-location", "version": {"requires": "21.0.1"}}], "files": [{"name": "expo.modules.location-18.1.6.aar", "url": "expo.modules.location-18.1.6.aar", "size": 184502, "sha512": "0607b95965c1e90bd336ce496b23bd0b955acb9aa1334af6379b3f727a98d9508a8edf99e0de0e686a0b1d06cd49041c5345666b3f1db63796d62fa4cd17e70c", "sha256": "784e0a683f0b9cf66cfc21bc85f3226264f4836bb830be3e45ccb18a786b540c", "sha1": "d3ffe4a8a3e378d262dec80358494753fdf9b5b5", "md5": "db113ce2039cad5fa994e932780abdf9"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7", "version": {"requires": "2.0.21"}}, {"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.7.1"}}, {"group": "com.google.android.gms", "module": "play-services-location", "version": {"requires": "21.0.1"}}], "files": [{"name": "expo.modules.location-18.1.6.aar", "url": "expo.modules.location-18.1.6.aar", "size": 184502, "sha512": "0607b95965c1e90bd336ce496b23bd0b955acb9aa1334af6379b3f727a98d9508a8edf99e0de0e686a0b1d06cd49041c5345666b3f1db63796d62fa4cd17e70c", "sha256": "784e0a683f0b9cf66cfc21bc85f3226264f4836bb830be3e45ccb18a786b540c", "sha1": "d3ffe4a8a3e378d262dec80358494753fdf9b5b5", "md5": "db113ce2039cad5fa994e932780abdf9"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "expo.modules.location-18.1.6-sources.jar", "url": "expo.modules.location-18.1.6-sources.jar", "size": 22916, "sha512": "ea8ee4997b883d87862e4bf9be7f825c134db18f7bf963e6eef6bc58009fdf681db683d96abd99d8f7e8b3e77668496ff242690db54ea9dd21b67e6f93cd6990", "sha256": "6cee5095a2544eab12b9238d3508e6f84472d5d4456f3ae025d2ca0004d5ac9f", "sha1": "92bdf64a7d26f40abe06efdd8a75fb5aea081a5b", "md5": "7901689a7c34acc7ae8af3462616529e"}]}]}